{"strategy_config": {"name": "EnhancedQuantumMomentumStrategy", "params": {"rsi_period": 14, "rsi_overbought": 68.0, "rsi_oversold": 32.0, "ema_short": 8, "ema_long": 21, "trend_ema": 50, "qubits": 8, "min_volatility": 0.015, "max_volatility": 0.05, "signal_threshold": 0.6, "quantum_weight": 0.55, "adaptive_risk": true, "divergence_filter": true, "data_lookback_period": 200, "take_profit_r_multiple": 2.375, "stop_loss_r_multiple": 1.2, "log_level": "INFO"}, "preload_candles_1h": 200, "preload_candles_5m": 720, "preload_candles_15m": 240, "preload_candles_1m": 200, "auto_download_history": true, "max_cache_age_minutes": 60, "ohlcv_failure_threshold": 3, "alternate_exchange": "kucoin"}, "qast_config": {"population_size": 120, "population_size_live": 6, "generations": 50, "generations_live": 5, "elite_size": 12, "mutation_rate": 0.15, "tournament_size": 5, "diversity_weight": 0.4, "enable_elitism": true, "fitness_weight_profitability": 0.5, "fitness_weight_consistency": 0.3, "fitness_weight_drawdown": 0.2, "min_generations_before_stop": 10, "early_stop_threshold": 0.001, "crossover_rate": 0.7, "use_adaptive_mutation": true, "selection_pressure": 2.5, "param_bounds": {"s1_tsvf_window": [10, 50], "s2_sma_short_period": [5, 30], "s2_sma_long_period": [50, 200], "s2_rsi_period": [7, 25], "s3_tsvf_window": [3, 12], "meta_decision_threshold": [0.05, 0.25], "tsvf_vector_size": [50, 150]}}, "ace_config": {"complexity_threshold_calm": 0.25, "complexity_threshold_normal": 0.35, "complexity_threshold_volatile": 0.5, "qubits_calm": 2, "qubits_normal": 8, "qubits_volatile": 8, "adaptation_aggressiveness": 0.8, "min_qubits": 2, "max_qubits": 10, "entanglement_calm": "linear", "entanglement_normal": "linear_random_cx", "entanglement_volatile": "full", "scr_depth_calm": 3, "scr_depth_normal": 6, "scr_depth_volatile": 9, "measure_frequency_calm": 2, "measure_frequency_normal": 1, "measure_frequency_volatile": 2, "enable_dynamic_risk_control": true, "risk_profile": "moderate", "override_on_metacognition": true, "dynamic_risk_config": {"dynamic_risk_parameters": {"atr_period": 14, "atr_multiplier_base": 1.15, "atr_multiplier_volatility_factor": 0.6, "take_profit_base_ratio": 1.2, "take_profit_volatility_adjustment": 0.3, "volatility_lookback_periods": 20, "volatility_threshold_low": 0.08, "volatility_threshold_high": 0.25, "recalibration_frequency_minutes": 5, "min_adjustment_threshold": 0.05, "max_adjustment_factor": 2.0, "regime_calm_multiplier": 0.8, "regime_volatile_multiplier": 1.3, "regime_normal_multiplier": 1.0}}, "encoders_extra": [{"name": "OTOC_Scrambling", "class": "OTOCEncoder", "params": {"data_keys": ["btc_otoc_1h"], "target_qubits": [3]}}], "quantum_sensitivity_refinements": {"adaptive_threshold_mode": true, "base_sensitivity_multiplier": 1.0, "volatility_adaptive_factor": 0.3, "entropy_sensitivity_threshold": 0.02, "otoc_sensitivity_threshold": 0.05, "coherence_sensitivity_threshold": 0.03, "pattern_detection_sensitivity": 0.75, "regime_based_adjustments": {"high_volatility": {"otoc_weight": 1.4, "entropy_weight": 1.2, "coherence_weight": 0.9, "threshold_multiplier": 0.85}, "low_volatility": {"otoc_weight": 0.8, "entropy_weight": 1.5, "coherence_weight": 1.3, "threshold_multiplier": 1.15}, "normal": {"otoc_weight": 1.0, "entropy_weight": 1.0, "coherence_weight": 1.0, "threshold_multiplier": 1.0}}, "dynamic_calibration": {"auto_recalibrate": true, "recalibration_frequency_minutes": 15, "performance_window_size": 50, "sensitivity_adjustment_rate": 0.1, "min_sensitivity": 0.3, "max_sensitivity": 2.0}}}, "risk_profile_settings": {"conservative": {"position_sizing_mode": "fixed_percentage", "max_position_percentage": 0.02, "stop_loss_percentage": 0.015, "take_profit_percentage": 0.025, "max_open_positions": 1, "enable_trailing_stop": false, "enable_dynamic_position_sizing": false, "quantum_sensitivity_boost": 1.0, "max_drawdown_pct": 10.0, "risk_per_trade_pct": 1.0, "max_daily_loss_pct": 5.0, "cooling_period_minutes": 30, "stop_loss_adjustment": 0.8, "min_lot_size": 0.0001}, "moderate": {"position_sizing_mode": "volatility_adjusted", "max_position_percentage": 0.05, "stop_loss_percentage": 0.025, "take_profit_percentage": 0.04, "max_open_positions": 2, "enable_trailing_stop": true, "enable_dynamic_position_sizing": true, "quantum_sensitivity_boost": 1.2, "max_drawdown_pct": 10.0, "risk_per_trade_pct": 1.0, "max_position_size_pct": 15.0, "max_daily_loss_pct": 5.0, "cooling_period_minutes": 30, "stop_loss_adjustment": 0.8, "min_lot_size": 0.0001}, "aggressive": {"position_sizing_mode": "kelly_criterion", "max_position_percentage": 0.08, "stop_loss_percentage": 0.035, "take_profit_percentage": 0.06, "max_open_positions": 3, "enable_trailing_stop": true, "enable_dynamic_position_sizing": true, "quantum_sensitivity_boost": 1.5, "max_drawdown_pct": 10.0, "risk_per_trade_pct": 1.0, "max_position_size_pct": 25.0, "max_daily_loss_pct": 5.0, "cooling_period_minutes": 30, "stop_loss_adjustment": 0.8, "min_lot_size": 0.0001}}, "temporal_detector_config": {"wavelet_depth": 4, "use_quantum_transform": true, "quantum_wavelet_shots": 3072, "qft_shots": 6144, "enhanced_sensitivity": {"pattern_recognition_threshold": 0.45, "anomaly_detection_threshold": 1.8, "trend_detection_sensitivity": 0.25, "reversal_detection_sensitivity": 0.35}}, "metacognition_config": {"max_pnl_history_size": 250, "cooldown_threshold": 2, "cooldown_confidence_step": 0.03, "skip_circuit_mutation_threshold": 2, "quantum_score_window_size": 75, "quantum_score_sigma": 0.015, "buy_score_threshold": 0.38, "sell_score_threshold": 0.32, "enhanced_pattern_detection": {"similarity_threshold_adaptive": true, "entropy_change_threshold": 0.01, "coherence_stability_threshold": 0.95, "otoc_variability_threshold": 0.08}}, "qpm_config": {"similarity_threshold": 0.2, "max_patterns": 500, "enable_warmstart": true, "warmstart_min_patterns": 10, "enable_pattern_pruning": true, "pruning_threshold": 0.15, "pattern_ttl_seconds": 3600, "persistence_path": "data/cache/qpm_memory.json", "pca_target_dim": 1024, "enhanced_retrieval": {"multi_modal_search": true, "context_aware_ranking": true, "temporal_weighting": true, "sensitivity_based_filtering": true}}, "fibonacci_wave_hype_config": {"name": "FibonacciWaveHypeStrategy", "enabled": true, "params": {"fib_lookback": 50, "sentiment_cache_ttl": 300, "timeframe_specific": {"1m": {"hype_threshold": 0.42, "wave_min_strength": 0.32, "quantum_boost_factor": 1.02, "holographic_weight": 0.4, "tsvf_validation_threshold": 0.65}, "5m": {"hype_threshold": 0.36, "wave_min_strength": 0.26, "quantum_boost_factor": 1.05, "holographic_weight": 0.5, "tsvf_validation_threshold": 0.55}, "15m": {"hype_threshold": 0.28, "wave_min_strength": 0.22, "quantum_boost_factor": 1.08, "holographic_weight": 0.6, "tsvf_validation_threshold": 0.45}, "1h": {"hype_threshold": 0.22, "wave_min_strength": 0.18, "quantum_boost_factor": 1.12, "holographic_weight": 0.7, "tsvf_validation_threshold": 0.35}}, "multi_timeframe_config": {"timeframe_weights": {"1m": 0.3, "5m": 0.4, "15m": 0.6, "1h": 0.8}, "min_confidence_threshold": 0.12, "convergence_threshold": 0.7, "divergence_penalty": 0.5, "require_primary_signal": true, "max_timeframe_age_minutes": 5, "cache_enabled": true, "cache_ttl_minutes": 2}, "fibonacci_levels": {"primary": [0.236, 0.382, 0.618], "secondary": [0.146, 0.5, 0.786], "extensions": [1.272, 1.618, 2.618]}, "wave_detection": {"min_wave_bars": 5, "max_wave_bars": 20, "volume_confirmation": true, "momentum_threshold": 0.02}, "risk_management": {"max_position_size": 0.1, "stop_loss_fib_level": 0.786, "take_profit_fib_level": 1.618, "dynamic_sizing": true}}, "integration": {"holographic_engine": true, "tsvf_calculator": true, "quantum_metrics": true, "sentiment_analysis": true}, "backtesting": {"lookback_days": 90, "benchmark_strategies": ["QualiaTSVFStrategy", "EnhancedQuantumMomentumStrategy"], "performance_metrics": ["sharpe_ratio", "max_drawdown", "win_rate", "profit_factor"]}}, "quantum_universe_config": {"base_lambda": 0.05, "alpha": 0.15, "thermal_coefficient": 0.08, "hawking_factor": 0.05, "measure_frequency": 2, "otoc_frequency": 3, "scr_depth": 4, "shots": 2048, "qpu_steps": 6, "enhanced_metrics": {"adaptive_otoc_calculation": true, "regime_based_operators": true, "sensitivity_filtering": true, "harmonic_phase_detection": true}}, "qmc_config": {"trading_primary_timeframe": "1m", "trading_timeframes": ["1m"], "convert_mismatched_timeframes": true, "shots": 2048, "thermal_noise": true, "temperature": 0.08, "qpu_steps": 6, "measure_frequency": 2, "encoders": [{"id": "default_qualia_encoders", "encoders": [{"name": "PM", "class": "PriceMomentumEncoder", "params": {"name": "PM", "data_keys": ["BTC/USDT_1m_price_change"], "target_qubits": [0], "scaling_factor": 50}}, {"name": "VOL", "class": "VolatilityEncoder", "params": {"name": "VOL", "data_keys": ["BTC/USDT_1m_volatility"], "target_qubits": [1]}}, {"name": "Volume_Ratio", "class": "VolumeRatioEncoder", "params": {"name": "VR", "data_keys": ["BTC/USDT_1m_volume_ratio"], "target_qubits": [2]}}, {"name": "RSI_Phase", "class": "RSIPhaseEncoder", "params": {"name": "RSI_P", "data_keys": ["BTC/USDT_1m_rsi"], "target_qubits": [3]}}, {"name": "Volume_Ratio_Amp", "class": "VolumeRatioAmplitudeEncoder", "params": {"name": "VR_AMP", "data_keys": ["BTC/USDT_1m_volume_ratio"], "target_qubits": [4]}}]}]}, "universe_config": {"min_counts_diversity_ratio": 0.1, "max_circuit_depth": 80, "max_circuit_operations": 250, "max_exact_qft_qubits": 14}, "hyperparams": {"source": "../qualia/config/hyperparams.yaml", "note": "Parâmetros de amplificação e confiança movidos para hyperparams.yaml"}}