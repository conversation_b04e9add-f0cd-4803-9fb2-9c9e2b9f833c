# Guia de Configuração Específica por Timeframe - QUALIA

## Visão Geral

O sistema QUALIA agora suporta configuração específica por timeframe para os parâmetros críticos da estratégia Fibonacci Wave Hype (FWH). Esta funcionalidade permite otimizar os parâmetros de acordo com as características únicas de cada timeframe.

## Parâmetros Configuráveis por Timeframe

Os seguintes parâmetros podem ser configurados especificamente para cada timeframe:

- **hype_threshold**: Threshold seletivo para sinais de qualidade
- **wave_min_strength**: Força mínima para momentum real  
- **quantum_boost_factor**: Boost conservador
- **holographic_weight**: Peso holográfico balanceado
- **tsvf_validation_threshold**: Validação rigorosa

## Timeframes Suportados

- **1m**: Scalping de alta frequência
- **5m**: Confirmação rápida
- **15m**: Detecção de ondas de hype
- **1h**: Tendência principal

## Estrutura de Configuração

### Arquivo: `config/fwh_scalp_config.yaml`

```yaml
fibonacci_wave_hype_config:
  params:
    # Parâmetros globais (aplicados se não especificado por timeframe)
    fib_lookback: 21
    sentiment_cache_ttl: 60
    
    # Configuração específica por timeframe
    timeframe_specific:
      "1m":
        hype_threshold: 0.42          # Mais alto para filtrar ruído
        wave_min_strength: 0.32       # Força aumentada
        quantum_boost_factor: 1.02    # Boost conservador
        holographic_weight: 0.4       # Peso reduzido
        tsvf_validation_threshold: 0.65 # Validação rigorosa
        
      "5m":
        hype_threshold: 0.36          # Balanceado
        wave_min_strength: 0.26       # Força padrão
        quantum_boost_factor: 1.05    # Boost conservador
        holographic_weight: 0.5       # Peso balanceado
        tsvf_validation_threshold: 0.55 # Validação rigorosa
        
      "15m":
        hype_threshold: 0.28          # Reduzido para ondas maiores
        wave_min_strength: 0.22       # Força reduzida
        quantum_boost_factor: 1.08    # Boost ligeiramente maior
        holographic_weight: 0.6       # Peso aumentado
        tsvf_validation_threshold: 0.45 # Validação flexível
        
      "1h":
        hype_threshold: 0.22          # Baixo para tendências longas
        wave_min_strength: 0.18       # Força mínima reduzida
        quantum_boost_factor: 1.12    # Boost maior
        holographic_weight: 0.7       # Peso máximo
        tsvf_validation_threshold: 0.35 # Validação flexível
```

## Lógica de Carregamento de Parâmetros

A estratégia FWH carrega os parâmetros seguindo esta ordem de prioridade:

1. **Parâmetros explícitos** passados na inicialização
2. **Parâmetros específicos do timeframe** atual
3. **Parâmetros globais** do arquivo de configuração
4. **Valores padrão** hardcoded

### Exemplo de Código

```python
from qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy

# Inicializar para timeframe específico
strategy_1m = FibonacciWaveHypeStrategy(
    symbol="BTC/USDT",
    timeframe="1m",
    context={"timeframe": "1m"}
)

strategy_1h = FibonacciWaveHypeStrategy(
    symbol="BTC/USDT", 
    timeframe="1h",
    context={"timeframe": "1h"}
)

# Os parâmetros serão carregados automaticamente baseados no timeframe
print(f"1m hype_threshold: {strategy_1m.hype_threshold}")  # 0.42
print(f"1h hype_threshold: {strategy_1h.hype_threshold}")  # 0.22
```

## Filosofia dos Valores por Timeframe

### 1m (Alta Frequência)
- **Thresholds mais altos**: Filtrar ruído de mercado
- **Validação rigorosa**: Evitar falsos positivos
- **Boost conservador**: Evitar over-amplificação

### 5m (Confirmação)
- **Valores balanceados**: Equilíbrio entre sensibilidade e precisão
- **Peso holográfico médio**: Capturar padrões sem over-fitting

### 15m (Ondas de Hype)
- **Thresholds reduzidos**: Capturar ondas de médio prazo
- **Peso holográfico aumentado**: Detectar padrões emergentes

### 1h (Tendência Principal)
- **Thresholds baixos**: Capturar movimentos de longo prazo
- **Boost máximo**: Amplificar sinais de tendência
- **Peso holográfico máximo**: Máxima sensibilidade a padrões

## Validação e Testes

Execute o script de validação para verificar a configuração:

```bash
python scripts/validate_timeframe_config.py
```

## Calibração Automática

O sistema de calibração foi atualizado para suportar timeframes específicos:

```bash
python scripts/calibrate_fwh_parameters.py
```

Os melhores parâmetros encontrados serão aplicados a todos os timeframes automaticamente.

## Arquivos Afetados

- `config/fwh_scalp_config.yaml` - Configuração principal
- `config/strategy_parameters.yaml` - Parâmetros de estratégia
- `config/strategy_parameters.json` - Versão JSON
- `src/qualia/strategies/fibonacci_wave_hype/core.py` - Lógica de carregamento
- `scripts/calibrate_fwh_parameters.py` - Sistema de calibração

## Benefícios

1. **Otimização por Contexto**: Cada timeframe tem parâmetros otimizados
2. **Redução de Ruído**: Filtros mais rigorosos em timeframes menores
3. **Captura de Tendências**: Sensibilidade aumentada em timeframes maiores
4. **Flexibilidade**: Fácil ajuste sem modificar código
5. **Consistência**: Mesma estrutura em todos os arquivos de configuração

## Configuração de Pesos de Timeframes

### Centralização no Arquivo YAML

Os pesos dos timeframes para cálculo de confiança consolidada agora estão **completamente centralizados** no arquivo de configuração YAML. Não há mais valores hardcoded no código.

```yaml
fibonacci_wave_hype_config:
  params:
    # Configuração do consolidador multi-timeframe
    multi_timeframe_config:
      timeframe_weights:
        "1m": 0.3                 # Timeframe de entrada - timing preciso
        "5m": 0.4                 # Timeframe de confirmação - validação rápida
        "15m": 0.6                # Timeframe de ondas de hype - detecção de padrões
        "1h": 0.8                 # Timeframe de tendência principal - contexto macro
      min_confidence_threshold: 0.12
      convergence_threshold: 0.7
      divergence_penalty: 0.5
      require_primary_signal: true
      max_timeframe_age_minutes: 5
      cache_enabled: true
      cache_ttl_minutes: 2
```

### Validação Automática

O sistema agora inclui validação rigorosa:
- **Obrigatório**: `timeframe_weights` deve estar presente na configuração
- **Completo**: Todos os timeframes (1m, 5m, 15m, 1h) devem ter pesos definidos
- **Válido**: Pesos devem ser números positivos

### Validação dos Pesos

Execute o script de validação específico:

```bash
python scripts/validate_timeframe_weights.py
```

## Próximos Passos

1. Executar backtests com a nova configuração
2. Monitorar performance em ambiente de paper trading
3. Ajustar valores baseado em resultados empíricos
4. Considerar expansão para outros parâmetros da estratégia
