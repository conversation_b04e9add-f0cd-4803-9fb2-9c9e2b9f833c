#!/usr/bin/env python3
"""
Script para testar a correção dos thresholds específicos por timeframe
na estratégia FibonacciWaveHypeStrategy.
"""

import sys
import os
import logging
from pathlib import Path

# Adicionar o diretório raiz ao path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configurar logging para capturar os detalhes
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_threshold_fix.log')
    ]
)
logger = logging.getLogger(__name__)

def test_threshold_loading():
    """Testa se os thresholds específicos por timeframe estão sendo carregados corretamente"""
    
    try:
        from src.qualia.strategies.fibonacci_wave_hype.core import FibonacciWaveHypeStrategy
        from src.qualia.config.config_manager import ConfigManager
        
        logger.info("🧪 TESTANDO CARREGAMENTO DE THRESHOLDS ESPECÍFICOS POR TIMEFRAME")
        logger.info("=" * 70)
        
        # Configurações esperadas do arquivo fwh_scalp_config.yaml
        expected_thresholds = {
            "1m": 0.42,
            "5m": 0.36,
            "15m": 0.28,
            "1h": 0.22
        }
        
        # Testar cada timeframe
        results = {}
        
        for timeframe in expected_thresholds.keys():
            logger.info(f"\n📊 TESTANDO TIMEFRAME: {timeframe}")
            logger.info("-" * 40)
            
            try:
                # Usar o arquivo de configuração correto
                config_path = Path(__file__).parent.parent / "config" / "fwh_scalp_config.yaml"
                config_manager = ConfigManager(config_path=str(config_path))
                
                # Criar estratégia com timeframe específico
                strategy = FibonacciWaveHypeStrategy(
                    params={},
                    context={"timeframe": timeframe},
                    config_manager=config_manager
                )
                
                # Verificar threshold carregado
                actual_threshold = strategy.hype_threshold
                expected_threshold = expected_thresholds[timeframe]
                
                logger.info(f"✅ Threshold carregado: {actual_threshold}")
                logger.info(f"📋 Threshold esperado: {expected_threshold}")
                
                # Verificar se está correto
                if abs(actual_threshold - expected_threshold) < 0.001:
                    logger.info(f"✅ SUCESSO: Threshold correto para {timeframe}")
                    results[timeframe] = "PASS"
                else:
                    logger.error(f"❌ FALHA: Threshold incorreto para {timeframe}")
                    logger.error(f"   Esperado: {expected_threshold}, Obtido: {actual_threshold}")
                    results[timeframe] = "FAIL"
                
            except Exception as e:
                logger.error(f"❌ ERRO ao testar {timeframe}: {e}")
                results[timeframe] = "ERROR"
        
        # Resumo dos resultados
        logger.info("\n" + "=" * 70)
        logger.info("📋 RESUMO DOS TESTES")
        logger.info("=" * 70)
        
        passed = sum(1 for result in results.values() if result == "PASS")
        failed = sum(1 for result in results.values() if result == "FAIL")
        errors = sum(1 for result in results.values() if result == "ERROR")
        
        for timeframe, result in results.items():
            status_icon = "✅" if result == "PASS" else "❌" if result == "FAIL" else "⚠️"
            logger.info(f"{status_icon} {timeframe}: {result}")
        
        logger.info(f"\n📊 ESTATÍSTICAS:")
        logger.info(f"   ✅ Passou: {passed}/{len(results)}")
        logger.info(f"   ❌ Falhou: {failed}/{len(results)}")
        logger.info(f"   ⚠️ Erros: {errors}/{len(results)}")
        
        if passed == len(results):
            logger.info("🎉 TODOS OS TESTES PASSARAM! Bug corrigido com sucesso.")
            return True
        else:
            logger.error("💥 ALGUNS TESTES FALHARAM! Bug ainda presente.")
            return False
            
    except ImportError as e:
        logger.error(f"❌ Erro de importação: {e}")
        logger.error("Certifique-se de que o QUALIA está instalado corretamente")
        return False
    except Exception as e:
        logger.error(f"❌ Erro inesperado: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False


def test_config_file_existence():
    """Verifica se os arquivos de configuração existem"""
    logger.info("\n🔍 VERIFICANDO ARQUIVOS DE CONFIGURAÇÃO")
    logger.info("-" * 50)
    
    config_files = [
        "config/fwh_scalp_config.yaml",
        "config/strategy_parameters.yaml"
    ]
    
    for config_file in config_files:
        config_path = Path(__file__).parent.parent / config_file
        if config_path.exists():
            logger.info(f"✅ {config_file} - ENCONTRADO")
        else:
            logger.error(f"❌ {config_file} - NÃO ENCONTRADO")


def main():
    """Função principal de teste"""
    logger.info("🚀 Iniciando teste de correção dos thresholds...")
    
    # Verificar arquivos de configuração
    test_config_file_existence()
    
    # Testar carregamento de thresholds
    success = test_threshold_loading()
    
    logger.info(f"\n📄 Log detalhado salvo em: test_threshold_fix.log")
    
    if success:
        logger.info("🎉 TESTE CONCLUÍDO COM SUCESSO!")
        return 0
    else:
        logger.error("💥 TESTE FALHOU!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
