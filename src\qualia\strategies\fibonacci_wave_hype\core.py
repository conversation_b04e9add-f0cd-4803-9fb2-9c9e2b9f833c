"""
Fibonacci Wave Hype Strategy - Versão Quântica

Combina análise clássica de Fibonacci com:
- Sentiment holográfico do HolographicFarsightEngine
- Métricas quânticas para validação temporal
- Integração com TSVF para detecção de ondas emergentes
"""

from __future__ import annotations

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List
from datetime import datetime
import uuid
import time

from ...utils.logger import get_logger

try:
    from ...utils.real_time_metrics_logger import (
        get_metrics_logger,
        log_trading_decision,
        create_timeframe_metrics,
        create_consolidated_metrics
    )
except ImportError:
    # Fallback se o módulo não estiver disponível
    def get_metrics_logger():
        return None
    def log_trading_decision(*args, **kwargs):
        pass
    def create_timeframe_metrics(*args, **kwargs):
        return None
    def create_consolidated_metrics(*args, **kwargs):
        return None
from ...strategies.strategy_interface import (
    TradingStrategy,
    register_strategy,
    TradingContext,
)
from ...farsight.holographic_extension import HolographicFarsightEngine
from ...strategies.nova_estrategia_qualia.tsvf import TSVFCalculator
from ...config.config_manager import ConfigManager
from ...market.binance_integration import BinanceIntegration
from ...market.market_data_client import MarketDataClient
from ..strategy_utils import make_signal_df

from .indicators import (
    calculate_fibonacci_levels,
    detect_wave_patterns,
    calculate_hype_momentum
)
from .sentiment_integration import integrate_holographic_sentiment
from .multi_timeframe_consolidator import (
    MultiTimeframeSignalConsolidator,
    TimeframeSignal,
    ConsolidatedSignal
)

logger = get_logger(__name__)

@register_strategy(
    name="FibonacciWaveHypeStrategy",
    category="quantum_fibonacci",
    description="Detecção quântica de ondas de hype usando Fibonacci holográfico",
    version="1.0.0",
    tags=["fibonacci", "hype", "holographic", "quantum"],
)
class FibonacciWaveHypeStrategy(TradingStrategy):
    """Estratégia de detecção de ondas de hype com Fibonacci quântico."""
    
    def __init__(
        self,
        symbol: str = None,
        timeframe: str = None,
        parameters: Optional[Dict] = None,
        params: Optional[Dict] = None,
        context: Optional[Dict] = None,
        config_manager: Optional[ConfigManager] = None
    ):
        # Compatibilidade com diferentes assinaturas
        if symbol and timeframe:
            # Modo legacy: symbol, timeframe, parameters
            context = context or {}
            context.update({"symbol": symbol, "timeframe": timeframe})
            params = parameters or params or {}
        else:
            # Modo novo: params, context, config_manager
            params = params or parameters or {}
            context = context or {}

        # Chama construtor da classe base
        super().__init__(params=params, context=context, config_manager=config_manager)

        # Configuração
        if not hasattr(self, 'config_manager') or self.config_manager is None:
            self.config_manager = config_manager or ConfigManager()

        try:
            self.config_manager.load()
        except Exception as e:
            logger.debug(f"[FWH] Erro ao carregar config: {e}")

        # Parâmetros Fibonacci (com fallback para config e suporte a timeframe específico)
        fwh_config = self.config_manager.data.get("fibonacci_wave_hype_config", {})
        fwh_params = fwh_config.get("params", {})

        # Obter timeframe atual do contexto
        current_timeframe = context.get("timeframe", self.timeframe if hasattr(self, 'timeframe') else "5m")

        # Carregar parâmetros específicos do timeframe se disponível
        timeframe_specific = fwh_params.get("timeframe_specific", {})
        timeframe_params = timeframe_specific.get(current_timeframe, {})

        # Função helper para obter parâmetro com prioridade: params > timeframe_specific > global > default
        def get_param(key, default_value):
            return params.get(key, timeframe_params.get(key, fwh_params.get(key, default_value)))

        self.fib_lookback = get_param("fib_lookback", 50)
        self.hype_threshold = get_param("hype_threshold", 0.25)
        self.wave_min_strength = get_param("wave_min_strength", 0.3)
        self.quantum_boost_factor = get_param("quantum_boost_factor", 1.2)
        self.holographic_weight = get_param("holographic_weight", 0.7)
        self.tsvf_validation_threshold = get_param("tsvf_validation_threshold", 0.5)

        # Log dos parâmetros carregados para debug
        logger.info(f"[FWH] Parâmetros para {current_timeframe}: "
                   f"hype_threshold={self.hype_threshold}, "
                   f"wave_min_strength={self.wave_min_strength}, "
                   f"quantum_boost_factor={self.quantum_boost_factor}, "
                   f"holographic_weight={self.holographic_weight}, "
                   f"tsvf_validation_threshold={self.tsvf_validation_threshold}")

        # Integração holográfica
        self.holographic_engine: Optional[HolographicFarsightEngine] = None
        self.tsvf_calculator = TSVFCalculator()

        # Integração com Binance (infraestrutura existente)
        self.binance_integration: Optional[BinanceIntegration] = None
        self.market_data_client: Optional[MarketDataClient] = None

        # Cache de ondas detectadas
        self.detected_waves: List[Dict] = []
        
        # Consolidador multi-timeframe
        consolidator_config = params.get("multi_timeframe_config", fwh_params.get("multi_timeframe_config", {}))
        self.signal_consolidator = MultiTimeframeSignalConsolidator(consolidator_config)

        # Configurações de análise (carregadas do YAML)
        analysis_config = fwh_params.get("analysis_config", {})

        # Timeframes suportados para análise multi-timeframe
        self.supported_timeframes = params.get("supported_timeframes",
                                              analysis_config.get("supported_timeframes", ["1m", "5m", "15m", "1h"]))
        self.primary_timeframe = params.get("primary_timeframe",
                                           analysis_config.get("primary_timeframe", "1m"))

        # Configurações de validação de dados
        self.min_data_config = analysis_config.get("min_data_periods", {})
        self.adaptive_periods_config = analysis_config.get("adaptive_min_periods", {})
        self.tsvf_config = analysis_config.get("tsvf_config", {})
        self.fibonacci_levels_config = analysis_config.get("fibonacci_trading_levels", {})
        self.confidence_config = analysis_config.get("confidence_config", {})

        # Estado da estratégia
        self.initialized = False
        
        # Metrics logger
        self.metrics_logger = get_metrics_logger()

        logger.info(f"[FWH] Inicializada para {symbol} - {timeframe} com threshold {self.hype_threshold}")

    def analyze_market(
        self,
        market_data: pd.DataFrame,
        trading_context: Any = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Analisa o mercado usando Fibonacci Wave Hype.

        Args:
            market_data: Dados históricos de mercado
            trading_context: Contexto de trading (opcional)
            **kwargs: Argumentos adicionais

        Returns:
            Análise do mercado com sinais FWH
        """
        try:
            if len(market_data) < self.fib_lookback:
                return {"signal": "HOLD", "confidence": 0.0, "reason": "Dados insuficientes"}

            # 1. Calcula níveis de Fibonacci
            fib_levels = calculate_fibonacci_levels(
                market_data["high"].tail(self.fib_lookback),
                market_data["low"].tail(self.fib_lookback)
            )

            # 2. Detecta padrões de ondas
            wave_patterns = detect_wave_patterns(market_data, fib_levels)

            # 3. Calcula momentum de hype
            hype_momentum = calculate_hype_momentum(market_data, wave_patterns)

            # 4. Integra sentiment holográfico se disponível
            holographic_boost_default = self.confidence_config.get("holographic_boost_default", 1.0)
            holographic_boost = holographic_boost_default
            if self.holographic_engine:
                try:
                    holographic_boost = integrate_holographic_sentiment(
                        self.holographic_engine, self.symbol
                    )
                except Exception as e:
                    logger.debug(f"[FWH] Erro no sentiment holográfico: {e}")

            # 5. Calcula sinal final
            signal_strength = hype_momentum * holographic_boost * self.quantum_boost_factor

            # 6. Determina sinal com diagnóstico detalhado (usando configurações do YAML)
            max_confidence = self.confidence_config.get("max_confidence", 1.0)
            min_signal_confidence = self.confidence_config.get("min_signal_confidence", 0.0)

            # Diagnóstico detalhado
            diagnostic_details = {
                "signal_strength": signal_strength,
                "hype_threshold": self.hype_threshold,
                "hype_momentum": hype_momentum,
                "holographic_boost": holographic_boost,
                "quantum_boost_factor": self.quantum_boost_factor,
                "wave_direction": wave_patterns["direction"],
                "components": f"hype_momentum={hype_momentum:.3f}, holographic_boost={holographic_boost:.3f}, quantum_boost={self.quantum_boost_factor:.3f}"
            }

            if signal_strength > self.hype_threshold and wave_patterns["direction"] == 1:
                signal = "BUY"
                confidence = min(signal_strength, max_confidence)
                reason = f"BUY: Signal strength {signal_strength:.3f} > threshold {self.hype_threshold:.3f}"
                logger.info(f"[FWH] {reason}")
                logger.info(f"[FWH] Details: {diagnostic_details['components']}")

            elif signal_strength > self.hype_threshold and wave_patterns["direction"] == -1:
                signal = "SELL"
                confidence = min(signal_strength, max_confidence)
                reason = f"SELL: Signal strength {signal_strength:.3f} > threshold {self.hype_threshold:.3f}"
                logger.info(f"[FWH] {reason}")
                logger.info(f"[FWH] Details: {diagnostic_details['components']}")

            else:
                signal = "HOLD"
                confidence = min_signal_confidence

                # Diagnóstico detalhado para HOLD
                if signal_strength <= self.hype_threshold:
                    reason = f"HOLD: Signal strength {signal_strength:.3f} < threshold {self.hype_threshold:.3f}"
                    specific_reason = "Força do sinal insuficiente"
                elif wave_patterns["direction"] == 0:
                    reason = f"HOLD: Direção indefinida (direction={wave_patterns['direction']})"
                    specific_reason = "Padrão de onda sem direção clara"
                else:
                    reason = f"HOLD: Critérios não atendidos"
                    specific_reason = "Múltiplos critérios falharam"

                logger.info(f"[FWH] {reason}")
                logger.info(f"[FWH] Details: {diagnostic_details['components']}")
                logger.info(f"[FWH] Reason: {specific_reason}")

            return {
                "signal": signal,
                "confidence": confidence,
                "fib_levels": fib_levels,
                "wave_patterns": wave_patterns,
                "hype_momentum": hype_momentum,
                "holographic_boost": holographic_boost,
                "signal_strength": signal_strength,
                "diagnostic": {
                    "reason": reason if 'reason' in locals() else f"{signal}: Standard execution",
                    "specific_reason": specific_reason if 'specific_reason' in locals() else "Critérios atendidos",
                    "details": diagnostic_details
                }
            }

        except Exception as e:
            logger.error(f"[FWH] Erro na análise de mercado: {e}")
            return {"signal": "HOLD", "confidence": 0.0, "error": str(e)}

    def initialize(self, context: Dict[str, Any]) -> None:
        """Inicializa integrações holográficas e Binance."""
        try:
            # Integração com HolographicFarsightEngine se disponível
            if "holographic_engine" in context:
                self.holographic_engine = context["holographic_engine"]
                logger.info("[FWH] Integração holográfica ativada")

            # Integração com Binance (infraestrutura existente)
            if "binance_integration" in context:
                self.binance_integration = context["binance_integration"]
                self.market_data_client = MarketDataClient(self.binance_integration)
                logger.info("[FWH] Binance integration conectada")
            elif "exchange_client" in context:
                # Fallback para cliente genérico
                self.market_data_client = MarketDataClient(context["exchange_client"])
                logger.info("[FWH] Exchange client conectado")

            self.initialized = True

        except Exception as e:
            logger.error(f"[FWH] Erro na inicialização: {e}")
            self.initialized = False

    async def get_real_market_data(
        self,
        symbol: str = None,
        timeframe: str = "1h",
        limit: int = None
    ) -> Optional[pd.DataFrame]:
        """
        Obtém dados reais de mercado usando a infraestrutura Binance existente.

        Args:
            timeframe: Timeframe dos dados
            limit: Número de períodos (usa self.fib_lookback se None)

        Returns:
            DataFrame com dados OHLCV ou None se erro
        """
        if not self.market_data_client:
            logger.warning("[FWH] Market data client não inicializado")
            return None

        try:
            limit = limit or self.fib_lookback
            symbol = symbol or self.symbol

            # Usa a infraestrutura existente para buscar dados OHLCV
            ohlcv_data = await self.market_data_client.fetch_ohlcv(
                symbol=symbol,
                timeframe=timeframe,
                limit=limit
            )

            if ohlcv_data is not None and not ohlcv_data.empty:
                logger.debug(f"[FWH] Dados reais obtidos: {len(ohlcv_data)} períodos para {symbol}")
                return ohlcv_data
            else:
                logger.warning(f"[FWH] Nenhum dado obtido para {symbol}")
                return None

        except Exception as e:
            logger.error(f"[FWH] Erro ao obter dados reais: {e}")
            return None

    async def get_real_ticker(self, symbol: str = None) -> Optional[Dict[str, Any]]:
        """
        Obtém ticker atual usando a infraestrutura Binance existente.

        Returns:
            Dados do ticker ou None se erro
        """
        if not self.market_data_client:
            logger.warning("[FWH] Market data client não inicializado")
            return None

        try:
            symbol = symbol or self.symbol
            ticker = await self.market_data_client.fetch_ticker(symbol)

            if ticker:
                logger.debug(f"[FWH] Ticker obtido para {symbol}: {ticker.get('last', 'N/A')}")
                return ticker
            else:
                logger.warning(f"[FWH] Ticker não disponível para {symbol}")
                return None

        except Exception as e:
            logger.error(f"[FWH] Erro ao obter ticker: {e}")
            return None
    
    def generate_signal(self, context: TradingContext) -> pd.DataFrame:
        """Gera sinal de trading baseado na estratégia Fibonacci Wave Hype.
        
        Utiliza análise multi-timeframe para consolidar sinais e melhorar precisão.
        
        Args:
            context: Contexto de trading com dados OHLCV
            
        Returns:
            DataFrame com sinal de trading consolidado ou vazio se não há sinal
        """
        # Delega para o método multi-timeframe otimizado
        return self.generate_multi_timeframe_signal(context)
    
    def _validate_with_tsvf(self, df: pd.DataFrame) -> float:
        """Valida sinal usando TSVF com configurações do YAML."""
        try:
            # Implementação simplificada do TSVF
            returns = df["close"].pct_change().dropna()

            # Configurações carregadas do YAML (sem valores padrão hardcoded)
            min_periods = self.min_data_config.get("tsvf_validation")
            neutral_score = self.tsvf_config.get("neutral_score")
            momentum_weight = self.tsvf_config.get("momentum_weight")
            volatility_epsilon = self.tsvf_config.get("volatility_epsilon")
            momentum_periods = self.tsvf_config.get("momentum_periods")

            # Validação: garantir que todas as configurações estão presentes
            if any(x is None for x in [min_periods, neutral_score, momentum_weight, volatility_epsilon, momentum_periods]):
                logger.error("[FWH] Configurações TSVF incompletas no arquivo YAML")
                return neutral_score or 0.5

            if len(returns) < min_periods:
                return neutral_score  # Neutro se poucos dados

            # Calcula volatilidade e momentum
            volatility = returns.std()
            momentum = returns.tail(momentum_periods).mean()

            # Score baseado em momentum e volatilidade
            tsvf_score = neutral_score + (momentum / (volatility + volatility_epsilon)) * momentum_weight

            return max(0.0, min(1.0, tsvf_score))

        except Exception as e:
            logger.debug(f"[FWH] Erro no TSVF: {e}")
            return self.tsvf_config.get("neutral_score", 0.5)
    
    def analyze_single_timeframe(self, market_data: pd.DataFrame, timeframe: str, symbol: str) -> TimeframeSignal:
        """Analisa um único timeframe e retorna sinal estruturado.
        
        Args:
            market_data: Dados OHLCV do timeframe
            timeframe: Timeframe analisado (ex: '1m', '5m')
            symbol: Símbolo do ativo
            
        Returns:
            Sinal estruturado para o timeframe
        """
        try:
            if len(market_data) < self.fib_lookback:
                insufficient_data_signal = TimeframeSignal(
                    timeframe=timeframe,
                    signal='hold',
                    confidence=0.0,
                    signal_strength=0.0,
                    hype_momentum=0.0,
                    holographic_boost=1.0,
                    tsvf_validation=0.5,
                    timestamp=datetime.now()
                )

                # Diagnóstico para dados insuficientes
                insufficient_data_signal.diagnostic_reason = f"HOLD: Dados insuficientes para {timeframe}"
                insufficient_data_signal.specific_reason = f"Períodos disponíveis ({len(market_data)}) < mínimo necessário ({self.fib_lookback})"
                insufficient_data_signal.diagnostic_details = {
                    "timeframe": timeframe,
                    "available_periods": len(market_data),
                    "required_periods": self.fib_lookback,
                    "signal_strength": 0.0,
                    "components": "Análise não realizada - dados insuficientes"
                }

                logger.debug(f"[FWH] {symbol}@{timeframe}: {insufficient_data_signal.diagnostic_reason}")
                logger.debug(f"[FWH] {symbol}@{timeframe}: {insufficient_data_signal.specific_reason}")

                return insufficient_data_signal
            
            # 1. Calcula níveis de Fibonacci
            fib_levels = calculate_fibonacci_levels(
                market_data["high"].tail(self.fib_lookback),
                market_data["low"].tail(self.fib_lookback)
            )
            
            # 2. Detecta padrões de ondas
            wave_patterns = detect_wave_patterns(market_data, fib_levels)
            
            # 3. Calcula momentum de hype
            hype_momentum = calculate_hype_momentum(market_data, wave_patterns)
            
            # 4. Integra sentiment holográfico
            holographic_boost_default = self.confidence_config.get("holographic_boost_default", 1.0)
            holographic_boost = holographic_boost_default
            if self.holographic_engine:
                try:
                    holographic_boost = integrate_holographic_sentiment(
                        self.holographic_engine, symbol
                    )
                except Exception as e:
                    logger.debug(f"[FWH] Erro no sentiment holográfico para {timeframe}: {e}")

            # 5. Validação TSVF
            tsvf_validation = self._validate_with_tsvf(market_data)

            # 6. Calcula sinal final
            signal_strength = hype_momentum * holographic_boost * tsvf_validation

            # 7. Determina direção do sinal com diagnóstico detalhado (usando configurações do YAML)
            max_confidence = self.confidence_config.get("max_confidence", 1.0)
            min_signal_confidence = self.confidence_config.get("min_signal_confidence", 0.0)

            # Diagnóstico detalhado para timeframe específico
            diagnostic_info = {
                "timeframe": timeframe,
                "signal_strength": signal_strength,
                "hype_threshold": self.hype_threshold,
                "hype_momentum": hype_momentum,
                "holographic_boost": holographic_boost,
                "tsvf_validation": tsvf_validation,
                "wave_direction": wave_patterns["direction"],
                "components": f"hype_momentum={hype_momentum:.3f}, holographic_boost={holographic_boost:.3f}, tsvf_validation={tsvf_validation:.3f}"
            }

            if signal_strength > self.hype_threshold:
                if wave_patterns["direction"] > 0:
                    signal = "buy"
                    confidence = min(signal_strength, max_confidence)
                    reason = f"BUY: Signal strength {signal_strength:.3f} > threshold {self.hype_threshold:.3f}"
                    specific_reason = f"Sinal de compra confirmado para {timeframe}"
                elif wave_patterns["direction"] < 0:
                    signal = "sell"
                    confidence = min(signal_strength, max_confidence)
                    reason = f"SELL: Signal strength {signal_strength:.3f} > threshold {self.hype_threshold:.3f}"
                    specific_reason = f"Sinal de venda confirmado para {timeframe}"
                else:
                    signal = "hold"
                    confidence = min_signal_confidence
                    reason = f"HOLD: Força suficiente ({signal_strength:.3f} > {self.hype_threshold:.3f}) mas direção indefinida"
                    specific_reason = f"Direção de onda indefinida para {timeframe}"
            else:
                signal = "hold"
                confidence = min_signal_confidence

                # Análise detalhada dos componentes que falharam
                failed_components = []
                if hype_momentum < 0.1:
                    failed_components.append(f"momentum baixo ({hype_momentum:.3f})")
                if holographic_boost < 0.8:
                    failed_components.append(f"boost holográfico baixo ({holographic_boost:.3f})")
                if tsvf_validation < self.tsvf_validation_threshold:
                    failed_components.append(f"TSVF reprovado ({tsvf_validation:.3f} < {self.tsvf_validation_threshold:.3f})")

                reason = f"HOLD: Signal strength {signal_strength:.3f} < threshold {self.hype_threshold:.3f}"
                if failed_components:
                    specific_reason = f"Componentes insuficientes para {timeframe}: {', '.join(failed_components)}"
                else:
                    specific_reason = f"Força geral insuficiente para {timeframe}"

            # Log detalhado
            logger.debug(f"[FWH] {symbol}@{timeframe}: {signal} (conf: {confidence:.3f}, strength: {signal_strength:.3f})")
            logger.debug(f"[FWH] {symbol}@{timeframe}: {reason}")
            logger.debug(f"[FWH] {symbol}@{timeframe}: Details: {diagnostic_info['components']}")
            logger.debug(f"[FWH] {symbol}@{timeframe}: Reason: {specific_reason}")
            
            # Log métricas do timeframe
            if self.metrics_logger:
                signal_data = {
                    'signal': signal,
                    'confidence': confidence,
                    'signal_strength': signal_strength,
                    'hype_momentum': hype_momentum,
                    'holographic_boost': holographic_boost,
                    'tsvf_validation': tsvf_validation,
                    'data_periods': len(market_data)
                }
                timeframe_metrics = create_timeframe_metrics(timeframe, signal_data)
                
                if timeframe_metrics:
                    # RealTimeMetricsLogger não tem método info, usar log_timeframe_metrics
                    pass
            
            # Criar TimeframeSignal com informações de diagnóstico
            tf_signal = TimeframeSignal(
                timeframe=timeframe,
                signal=signal,
                confidence=confidence,
                signal_strength=signal_strength,
                hype_momentum=hype_momentum,
                holographic_boost=holographic_boost,
                tsvf_validation=tsvf_validation,
                timestamp=datetime.now()
            )

            # Adicionar informações de diagnóstico como atributos extras
            tf_signal.diagnostic_reason = reason if 'reason' in locals() else f"{signal.upper()}: Standard execution"
            tf_signal.specific_reason = specific_reason if 'specific_reason' in locals() else "Critérios atendidos"
            tf_signal.diagnostic_details = diagnostic_info

            return tf_signal
            
        except Exception as e:
            logger.error(f"[FWH] Erro na análise {symbol}@{timeframe}: {e}")
            error_signal = TimeframeSignal(
                timeframe=timeframe,
                signal='hold',
                confidence=0.0,
                signal_strength=0.0,
                hype_momentum=0.0,
                holographic_boost=1.0,
                tsvf_validation=0.5,
                timestamp=datetime.now()
            )

            # Adicionar diagnóstico de erro
            error_signal.diagnostic_reason = f"HOLD: Erro na análise do timeframe {timeframe}"
            error_signal.specific_reason = f"Erro técnico: {str(e)}"
            error_signal.diagnostic_details = {
                "timeframe": timeframe,
                "error": str(e),
                "signal_strength": 0.0,
                "components": "Análise falhou - componentes não calculados"
            }

            return error_signal
    
    def generate_multi_timeframe_signal(self, context: TradingContext) -> pd.DataFrame:
        """Gera sinal consolidado de múltiplos timeframes.
        
        Args:
            context: Contexto de trading com dados OHLCV
            
        Returns:
            DataFrame com sinal consolidado ou vazio se não há sinal
        """
        try:
            # Obter símbolo do contexto
            current_symbol = getattr(context, 'symbol', self.symbol)
            logger.info(f"[FWH] Iniciando análise multi-timeframe para {current_symbol}")
            
            if not current_symbol:
                logger.warning("[FWH] Nenhum símbolo fornecido no contexto")
                return pd.DataFrame()
            
            # Obter dados primários (menor timeframe)
            primary_data = context.ohlcv
            if primary_data is None or len(primary_data) < self.fib_lookback:
                available_periods = len(primary_data) if primary_data is not None else 0
                logger.warning(f"[FWH] {current_symbol}: HOLD - Dados primários insuficientes")
                logger.warning(f"[FWH] {current_symbol}: Períodos disponíveis ({available_periods}) < mínimo necessário ({self.fib_lookback})")
                logger.warning(f"[FWH] {current_symbol}: Reason: Dados OHLCV insuficientes para análise Fibonacci")
                logger.warning(f"[FWH] {current_symbol}: Details: timeframe_primário={self.primary_timeframe}, fib_lookback={self.fib_lookback}")
                return pd.DataFrame()
            
            logger.info(f"[FWH] {current_symbol}: Dados primários - {len(primary_data)} períodos")
            
            # Lista para armazenar sinais de cada timeframe
            timeframe_signals = []
            
            # Analisa cada timeframe suportado
            for timeframe in self.supported_timeframes:
                try:
                    if timeframe == self.primary_timeframe:
                        # Usa dados primários diretamente
                        tf_data = primary_data
                    else:
                        # Reamostra dados para timeframe maior
                        tf_data = self.signal_consolidator.resample_data(primary_data, timeframe)
                        
                        # Validação adaptativa baseada no timeframe (configurações do YAML)
                        divisor_5m = self.adaptive_periods_config.get("5m", 8)
                        divisor_15m = self.adaptive_periods_config.get("15m", 10)
                        divisor_1h = self.adaptive_periods_config.get("1h", 15)
                        fallback_divisor = self.adaptive_periods_config.get("fallback_divisor", 5)

                        min_periods_by_timeframe = {
                            '5m': max(3, self.fib_lookback // divisor_5m),
                            '15m': max(2, self.fib_lookback // divisor_15m),
                            '1h': max(2, self.fib_lookback // divisor_1h)
                        }

                        min_required = min_periods_by_timeframe.get(timeframe, max(5, self.fib_lookback // fallback_divisor))
                        
                        if tf_data.empty or len(tf_data) < min_required:
                            logger.debug(f"[FWH] Dados insuficientes após resample para {timeframe}: {len(tf_data)} < {min_required}")
                            continue
                    
                    # Analisa timeframe
                    tf_signal = self.analyze_single_timeframe(tf_data, timeframe, current_symbol)
                    timeframe_signals.append(tf_signal)
                    
                    logger.info(f"[FWH] {current_symbol}@{timeframe}: {tf_signal.signal} (confiança: {tf_signal.confidence:.3f})")
                    
                except Exception as e:
                    logger.error(f"[FWH] Erro na análise do timeframe {timeframe}: {e}")
                    continue
            
            # Consolida sinais com diagnóstico detalhado
            if not timeframe_signals:
                logger.warning(f"[FWH] {current_symbol}: Nenhum sinal válido gerado")
                logger.warning(f"[FWH] {current_symbol}: HOLD - Falha na análise de todos os timeframes")
                logger.warning(f"[FWH] {current_symbol}: Reason: Dados insuficientes ou erros técnicos em todos os timeframes")
                return pd.DataFrame()

            consolidated = self.signal_consolidator.consolidate_signals(timeframe_signals)

            # Log detalhado do resultado consolidado
            logger.info(f"[FWH] {current_symbol}: Sinal consolidado - {consolidated.signal} (confiança: {consolidated.confidence:.3f})")
            logger.info(f"[FWH] {current_symbol}: {consolidated.reasoning}")

            # Log diagnóstico de cada timeframe
            for tf_signal in timeframe_signals:
                if hasattr(tf_signal, 'diagnostic_reason'):
                    logger.info(f"[FWH] {current_symbol}@{tf_signal.timeframe}: {tf_signal.diagnostic_reason}")
                    logger.debug(f"[FWH] {current_symbol}@{tf_signal.timeframe}: {tf_signal.specific_reason}")
                    if hasattr(tf_signal, 'diagnostic_details'):
                        logger.debug(f"[FWH] {current_symbol}@{tf_signal.timeframe}: Details: {tf_signal.diagnostic_details.get('components', 'N/A')}")

            # Diagnóstico da consolidação
            consolidation_details = {
                "total_timeframes": len(timeframe_signals),
                "buy_signals": len([s for s in timeframe_signals if s.signal == 'buy']),
                "sell_signals": len([s for s in timeframe_signals if s.signal == 'sell']),
                "hold_signals": len([s for s in timeframe_signals if s.signal == 'hold']),
                "avg_confidence": sum(s.confidence for s in timeframe_signals) / len(timeframe_signals),
                "convergence_score": consolidated.convergence_score,
                "primary_timeframe": consolidated.primary_timeframe
            }

            logger.info(f"[FWH] {current_symbol}: Consolidation details: {consolidation_details}")
            
            # Log métricas consolidadas
            if self.metrics_logger:
                consolidated_metrics = create_consolidated_metrics(consolidated)
                
                if consolidated_metrics:
                    # RealTimeMetricsLogger não tem método info, usar log_consolidated_metrics
                    pass
            
            # Gera DataFrame de sinal se válido
            if consolidated.signal in ["buy", "sell"] and consolidated.confidence > 0:
                current_price = primary_data["close"].iloc[-1]
                
                # Calcula níveis de stop/take profit baseados no timeframe primário
                primary_signal = next((s for s in timeframe_signals if s.timeframe == consolidated.primary_timeframe), timeframe_signals[0])
                
                # Usa dados do timeframe primário para calcular níveis
                if consolidated.primary_timeframe == self.primary_timeframe:
                    fib_data = primary_data
                else:
                    fib_data = self.signal_consolidator.resample_data(primary_data, consolidated.primary_timeframe)
                
                fib_levels = calculate_fibonacci_levels(
                    fib_data["high"].tail(self.fib_lookback),
                    fib_data["low"].tail(self.fib_lookback)
                )
                
                stop_loss, take_profit = self._calculate_fib_levels(
                    current_price, fib_levels, consolidated.signal
                )
                
                # Log decisão de trading usando função auxiliar
                if self.metrics_logger:
                    from qualia.utils.real_time_metrics_logger import log_trading_decision
                    
                    decision_id = str(uuid.uuid4())
                    
                    log_trading_decision(
                        decision_id=decision_id,
                        symbol=current_symbol,
                        current_price=current_price,
                        timeframe_signals=timeframe_signals,
                        consolidated_signal=consolidated,
                        trade_executed=True,
                        execution_reason=consolidated.reasoning,
                        stop_loss=stop_loss,
                        take_profit=take_profit,
                        volume=None,
                        position_size=None,
                        risk_percentage=None,
                        processing_time_ms=0.0,
                        data_quality_score=1.0,
                        market_conditions={},
                        system_state={}
                    )
                
                return make_signal_df(
                    signal=consolidated.signal,
                    confidence=consolidated.confidence,
                    price=current_price,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    fibonacci_level=consolidated.primary_timeframe,
                    hype_momentum=primary_signal.hype_momentum,
                    holographic_boost=primary_signal.holographic_boost,
                    tsvf_validation=primary_signal.tsvf_validation,
                    # Campos adicionais para multi-timeframe
                    convergence_score=consolidated.convergence_score,
                    supporting_timeframes=",".join(consolidated.supporting_timeframes),
                    consolidation_reasoning=consolidated.reasoning
                )
            else:
                # Diagnóstico detalhado para rejeição do sinal consolidado
                rejection_reason = []

                if consolidated.signal not in ["buy", "sell"]:
                    rejection_reason.append(f"sinal={consolidated.signal}")

                if consolidated.confidence <= 0:
                    rejection_reason.append(f"confiança={consolidated.confidence:.3f}")

                # Verificar threshold de confiança mínima do consolidador
                min_confidence_threshold = self.signal_consolidator.min_confidence_threshold
                if consolidated.confidence <= min_confidence_threshold:
                    rejection_reason.append(f"confiança ({consolidated.confidence:.3f}) <= threshold ({min_confidence_threshold:.3f})")

                rejection_details = ", ".join(rejection_reason) if rejection_reason else "critérios múltiplos"

                logger.info(f"[FWH] {current_symbol}: HOLD - Sinal consolidado rejeitado")
                logger.info(f"[FWH] {current_symbol}: Rejection reason: {rejection_details}")
                logger.info(f"[FWH] {current_symbol}: Consolidation details: signal={consolidated.signal}, confidence={consolidated.confidence:.3f}")
                logger.info(f"[FWH] {current_symbol}: Supporting timeframes: {', '.join(consolidated.supporting_timeframes) if consolidated.supporting_timeframes else 'none'}")

                # Log individual timeframe diagnostics for rejected signal
                logger.debug(f"[FWH] {current_symbol}: Individual timeframe analysis:")
                for tf_signal in timeframe_signals:
                    if hasattr(tf_signal, 'diagnostic_reason'):
                        logger.debug(f"[FWH] {current_symbol}@{tf_signal.timeframe}: {tf_signal.diagnostic_reason}")

                return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"[FWH] {current_symbol if 'current_symbol' in locals() else 'UNKNOWN'}: HOLD - Erro crítico na geração de sinal multi-timeframe")
            logger.error(f"[FWH] Error details: {str(e)}")
            logger.error(f"[FWH] Reason: Falha técnica durante análise multi-timeframe")
            logger.error(f"[FWH] Components: Análise interrompida por erro - componentes não calculados")

            import traceback
            logger.error(f"[FWH] Traceback: {traceback.format_exc()}")
            return pd.DataFrame()
    
    def _calculate_fib_levels(self, price: float, fib_levels: Dict, signal: str) -> tuple:
        """Calcula stop loss e take profit baseados em níveis de Fibonacci com configurações do YAML."""
        # Configurações carregadas do YAML (sem valores padrão hardcoded)
        buy_stop_multiplier = self.fibonacci_levels_config.get("buy_stop_loss_multiplier")
        buy_take_multiplier = self.fibonacci_levels_config.get("buy_take_profit_multiplier")
        sell_stop_multiplier = self.fibonacci_levels_config.get("sell_stop_loss_multiplier")
        sell_take_multiplier = self.fibonacci_levels_config.get("sell_take_profit_multiplier")

        # Validação: garantir que todas as configurações estão presentes
        if any(x is None for x in [buy_stop_multiplier, buy_take_multiplier, sell_stop_multiplier, sell_take_multiplier]):
            logger.error("[FWH] Configurações de níveis Fibonacci incompletas no arquivo YAML")
            # Usar preço atual como fallback de emergência
            return price, price

        if signal == "buy":
            stop_loss = fib_levels.get("support", price * buy_stop_multiplier)
            take_profit = fib_levels.get("resistance", price * buy_take_multiplier)
        else:
            stop_loss = fib_levels.get("resistance", price * sell_stop_multiplier)
            take_profit = fib_levels.get("support", price * sell_take_multiplier)

        return stop_loss, take_profit